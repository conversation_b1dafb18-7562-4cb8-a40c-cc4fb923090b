<?php

// Простой тест для проверки извлечения опциональных полей

// Получаем исходный код ресурса
$resourceFile = 'app/Http/Resources/Procurement/Acceptances/AcceptanceItemIndexResource.php';
$sourceCode = file_get_contents($resourceFile);

echo "=== Тестирование извлечения @optional полей ===\n\n";

// Извлекаем PHPDoc комментарий метода toArray
if (preg_match('/public function toArray.*?\{(.*?)\n    \}/s', $sourceCode, $matches)) {
    $methodContent = $matches[1];

    // Ищем все @var аннотации с @optional
    $optionalFields = [];
    $lines = explode("\n", $methodContent);

    foreach ($lines as $line) {
        // Паттерн: /** @var type $field_name Description @optional */
        if (preg_match('/\*\s*@var\s+[^$]+\$(\w+).*@optional/i', $line, $matches)) {
            $optionalFields[] = $matches[1];
        }
    }

    echo "Поля с @optional в @var аннотациях:\n";
    var_dump($optionalFields);
}

// Ищем аннотации @optional вне массива result
$optionalOutside = [];
if (preg_match_all('/\/\*\*\s*@optional\s+[^$]*\$(\w+)/i', $sourceCode, $matches)) {
    $optionalOutside = $matches[1];
}

echo "\nПоля с @optional вне массива:\n";
var_dump($optionalOutside);

// Ищем условно добавляемые поля
$conditionalFields = [];
if (preg_match_all('/if\s*\(\s*isset\(\$this->(\w+)\)\s*\)\s*\{[^}]*\$result\[[\'"]([\w]+)[\'"]\]/s', $sourceCode, $matches)) {
    $conditionalFields = $matches[2];
}

echo "\nУсловно добавляемые поля:\n";
var_dump($conditionalFields);

// Объединяем все результаты
$allOptional = array_unique(array_merge($optionalFields, $optionalOutside, $conditionalFields));

echo "\nВсе опциональные поля:\n";
var_dump($allOptional);
