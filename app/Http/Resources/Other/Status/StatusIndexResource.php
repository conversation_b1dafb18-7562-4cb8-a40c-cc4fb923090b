<?php

namespace App\Http\Resources\Other\Status;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class StatusIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор статуса */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->when(isset($this->created_at), $this->created_at),
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->when(isset($this->updated_at), $this->updated_at),
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->when(isset($this->deleted_at), $this->deleted_at),
            /** @var string|null $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(isset($this->cabinet_id), $this->cabinet_id),
            /** @var string $name Название статуса */
            'name' => $this->when(isset($this->name), (string) $this->name),
            /** @var string $color Цвет статуса */
            'color' => $this->when(isset($this->color), (string) $this->color),
            /** @var string $type_id Идентификатор типа статуса */
            'type_id' => $this->when(isset($this->type_id), $this->type_id),
        ];
    }
}
