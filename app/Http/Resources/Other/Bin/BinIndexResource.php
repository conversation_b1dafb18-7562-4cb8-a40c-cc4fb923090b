<?php

namespace App\Http\Resources\Other\Bin;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BinIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор элемента корзины */
            'id' => $this->id,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(isset($this->cabinet_id), $this->cabinet_id),
            /** @var string $record_id Идентификатор записи */
            'record_id' => $this->when(isset($this->record_id), $this->record_id),
            /** @var string $table_name Название таблицы */
            'table_name' => $this->when(isset($this->table_name), (string) $this->table_name),
            /** @var string|null $record_name Название записи */
            'record_name' => $this->when(isset($this->record_name), $this->record_name),
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->when(isset($this->deleted_at), $this->deleted_at),
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->when(isset($this->created_at), $this->created_at),
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->when(isset($this->updated_at), $this->updated_at),
        ];
    }
}
