<?php

namespace App\Http\Resources\Marketplaces\Wildberries\FBS\Orders;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FBSOrderIndexResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор заказа */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания записи */
            'created_at' => $this->when(isset($this->created_at), $this->created_at),
            /** @var string $updated_at Дата и время последнего обновления записи */
            'updated_at' => $this->when(isset($this->updated_at), $this->updated_at),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(isset($this->cabinet_id), $this->cabinet_id),
            /** @var string $integration_id Идентификатор интеграции */
            'integration_id' => $this->when(isset($this->integration_id), $this->integration_id),
            /** @var string|null $legal_entity_id Идентификатор юридического лица */
            'legal_entity_id' => $this->when(isset($this->legal_entity_id), $this->legal_entity_id),
            /** @var string|null $contractor_id Идентификатор контрагента */
            'contractor_id' => $this->when(isset($this->contractor_id), $this->contractor_id),
            /** @var string|null $department_id Идентификатор отдела */
            'department_id' => $this->when(isset($this->department_id), $this->department_id),
            /** @var string|null $employee_id Идентификатор сотрудника */
            'employee_id' => $this->when(isset($this->employee_id), $this->employee_id),
            /** @var string|null $warehouse_id Идентификатор склада */
            'warehouse_id' => $this->when(isset($this->warehouse_id), $this->warehouse_id),
            /** @var string|null $currency_id Идентификатор валюты */
            'currency_id' => $this->when(isset($this->currency_id), $this->currency_id),
            /** @var string|null $customer_order_id Идентификатор заказа покупателя */
            'customer_order_id' => $this->when(isset($this->customer_order_id), $this->customer_order_id),
            /** @var string $module_status Статус в нашей системе */
            'module_status' => $this->when(isset($this->module_status), $this->module_status),
            /** @var string|null $supplier_status Статус поставщика */
            'supplier_status' => $this->when(isset($this->supplier_status), $this->supplier_status),
            /** @var string|null $wb_status Статус в Wildberries */
            'wb_status' => $this->when(isset($this->wb_status), $this->wb_status),
            /** @var bool $has_unmatched_items Признак несопоставленных товаров */
            'has_unmatched_items' => $this->when(isset($this->has_unmatched_items), (bool) $this->has_unmatched_items),
            /** @var bool $needs_warehouse_mapping Требуется сопоставление склада */
            'needs_warehouse_mapping' => $this->when(isset($this->needs_warehouse_mapping), (bool) $this->needs_warehouse_mapping),
            /** @var string|null $number Номер заказа в нашей системе */
            'number' => $this->when(isset($this->number), $this->number),
            /** @var int|null $wb_number Номер заказа в Wildberries */
            'wb_number' => $this->when(isset($this->wb_number), $this->wb_number),
            /** @var string|null $tracking_number Номер отслеживания */
            'tracking_number' => $this->when(isset($this->tracking_number), $this->tracking_number),
            /** @var string|null $comment Комментарий к заказу */
            'comment' => $this->when(isset($this->comment), $this->comment),
            /** @var string|null $total_price Общая сумма заказа */
            'total_price' => $this->when(isset($this->total_price), $this->total_price ? (string) $this->total_price : null),
            /** @var bool $reserve Признак резервирования */
            'reserve' => $this->when(isset($this->reserve), (bool) $this->reserve),
            /** @var string|null $delivery_date Дата доставки */
            'delivery_date' => $this->when(isset($this->delivery_date), $this->delivery_date),
            /** @var string|null $delivery_type Тип доставки */
            'delivery_type' => $this->when(isset($this->delivery_type), $this->delivery_type),
            /** @var int|null $cargo_type Тип груза */
            'cargo_type' => $this->when(isset($this->cargo_type), $this->cargo_type),
            /** @var int|null $wb_warehouse_id Идентификатор склада WB */
            'wb_warehouse_id' => $this->when(isset($this->wb_warehouse_id), $this->wb_warehouse_id),
            /** @var string|null $office_list Список офисов */
            'office_list' => $this->when(isset($this->office_list), $this->office_list),
            /** @var bool $need_reshipment Требуется переотправка */
            'need_reshipment' => $this->when(isset($this->need_reshipment), (bool) $this->need_reshipment),
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->when(isset($this->deleted_at), $this->deleted_at),
        ];
    }
}
