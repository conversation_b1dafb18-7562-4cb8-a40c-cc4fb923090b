<?php

namespace App\Http\Resources\Marketplaces\Wildberries\FBS\Warehouses;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WarehouseIndexResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор склада */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания записи */
            'created_at' => $this->when(isset($this->created_at), $this->created_at),
            /** @var string $updated_at Дата и время последнего обновления записи */
            'updated_at' => $this->when(isset($this->updated_at), $this->updated_at),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(isset($this->cabinet_id), $this->cabinet_id),
            /** @var string $wildberries_integration_id Идентификатор интеграции Wildberries */
            'wildberries_integration_id' => $this->when(isset($this->wildberries_integration_id), $this->wildberries_integration_id),
            /** @var string $status Статус сопоставления склада */
            'status' => $this->when(isset($this->status), $this->status),
            /** @var string|null $name Название склада */
            'name' => $this->when(isset($this->name), $this->name),
            /** @var int $office_id ID склада WB */
            'office_id' => $this->when(isset($this->office_id), $this->office_id),
            /** @var int $seller_warehouse_id ID склада продавца */
            'seller_warehouse_id' => $this->when(isset($this->seller_warehouse_id), $this->seller_warehouse_id),
            /** @var string $warehouse_id ID склада в системе */
            'warehouse_id' => $this->when(isset($this->warehouse_id), $this->warehouse_id),
            /** @var int $cargo_type Тип товара (1-МГТ, 2-СГТ, 3-КГТ+) */
            'cargo_type' => $this->when(isset($this->cargo_type), $this->cargo_type),
            /** @var int $delivery_type Тип доставки (1-FBS, 2-DBS, 3-DBW) */
            'delivery_type' => $this->when(isset($this->delivery_type), $this->delivery_type),
        ];
    }
}
