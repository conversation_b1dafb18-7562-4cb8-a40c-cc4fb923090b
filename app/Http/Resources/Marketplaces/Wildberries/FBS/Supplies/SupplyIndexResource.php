<?php

namespace App\Http\Resources\Marketplaces\Wildberries\FBS\Supplies;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SupplyIndexResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор поставки */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания записи */
            'created_at' => $this->when(isset($this->created_at), $this->created_at),
            /** @var string $updated_at Дата и время последнего обновления записи */
            'updated_at' => $this->when(isset($this->updated_at), $this->updated_at),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(isset($this->cabinet_id), $this->cabinet_id),
            /** @var string $integration_id Идентификатор интеграции */
            'integration_id' => $this->when(isset($this->integration_id), $this->integration_id),
            /** @var string|null $supply_id ID поставки в Wildberries */
            'supply_id' => $this->when(isset($this->supply_id), $this->supply_id),
            /** @var string $name Название поставки */
            'name' => $this->when(isset($this->name), $this->name),
            /** @var bool $done Флаг завершения поставки */
            'done' => $this->when(isset($this->done), (bool) $this->done),
            /** @var string|null $closed_at Дата закрытия поставки */
            'closed_at' => $this->when(isset($this->closed_at), $this->closed_at),
            /** @var string|null $scanned_at Дата сканирования */
            'scanned_at' => $this->when(isset($this->scanned_at), $this->scanned_at),
            /** @var int|null $cargo_type Габаритный тип поставки */
            'cargo_type' => $this->when(isset($this->cargo_type), $this->cargo_type),
            /** @var string|null $created_at_wb Дата создания поставки в Wildberries */
            'created_at_wb' => $this->when(isset($this->created_at_wb), $this->created_at_wb),
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->when(isset($this->deleted_at), $this->deleted_at),
        ];
    }
}
