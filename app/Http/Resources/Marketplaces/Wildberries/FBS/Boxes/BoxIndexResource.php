<?php

namespace App\Http\Resources\Marketplaces\Wildberries\FBS\Boxes;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BoxIndexResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор коробки */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания записи */
            'created_at' => $this->when(isset($this->created_at), $this->created_at),
            /** @var string $updated_at Дата и время последнего обновления записи */
            'updated_at' => $this->when(isset($this->updated_at), $this->updated_at),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(isset($this->cabinet_id), $this->cabinet_id),
            /** @var string $integration_id Идентификатор интеграции */
            'integration_id' => $this->when(isset($this->integration_id), $this->integration_id),
            /** @var string $trbxIds Идентификатор коробки в системе Wildberries */
            'trbxIds' => $this->when(isset($this->trbxIds), $this->trbxIds),
            /** @var string $supply_id Идентификатор поставки */
            'supply_id' => $this->when(isset($this->supply_id), $this->supply_id),
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->when(isset($this->deleted_at), $this->deleted_at),
        ];
    }
}
