<?php

namespace App\Http\Resources\Files;

use App\Services\Storage\S3StorageService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FileIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        /** @var S3StorageService $storageService */
        $storageService = app(S3StorageService::class);

        return [
            /** @var string $id Уникальный идентификатор файла */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->when(isset($this->created_at), $this->created_at),
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->when(isset($this->updated_at), $this->updated_at),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(isset($this->cabinet_id), $this->cabinet_id),
            /** @var string $name Название файла */
            'name' => $this->when(isset($this->name), (string) $this->name),
            /** @var string $path URL файла */
            'path' => $this->when(isset($this->path), $storageService->getUrl($this->path, $this->is_private)),
            /** @var int $size Размер файла в байтах */
            'size' => $this->when(isset($this->size), (int) $this->size),
            /** @var string $mime_type MIME тип файла */
            'mime_type' => $this->when(isset($this->mime_type), (string) $this->mime_type),
            /** @var bool $is_private Приватный файл */
            'is_private' => $this->when(isset($this->is_private), (bool) $this->is_private),
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->when(isset($this->employee_id), $this->employee_id),
            /** @var string|null $type Тип файла */
            'type' => $this->when(isset($this->type), $this->type),
        ];
    }
}
