<?php

namespace App\Http\Resources\Warehouses\Contacts;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WarehouseAddressIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор адреса склада */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->when(isset($this->created_at), $this->created_at),
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->when(isset($this->updated_at), $this->updated_at),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(isset($this->cabinet_id), $this->cabinet_id),
            /** @var string $country_id Идентификатор страны */
            'country_id' => $this->when(isset($this->country_id), $this->country_id),
            /** @var string|null $postcode Почтовый индекс */
            'postcode' => $this->when(isset($this->postcode), $this->postcode),
            /** @var string|null $region Регион */
            'region' => $this->when(isset($this->region), $this->region),
            /** @var string|null $city Город */
            'city' => $this->when(isset($this->city), $this->city),
            /** @var string|null $street Улица */
            'street' => $this->when(isset($this->street), $this->street),
            /** @var string|null $house Дом */
            'house' => $this->when(isset($this->house), $this->house),
            /** @var string|null $office Офис */
            'office' => $this->when(isset($this->office), $this->office),
            /** @var string|null $other Другое */
            'other' => $this->when(isset($this->other), $this->other),
            /** @var string|null $comment Комментарий */
            'comment' => $this->when(isset($this->comment), $this->comment),
        ];
    }
}
