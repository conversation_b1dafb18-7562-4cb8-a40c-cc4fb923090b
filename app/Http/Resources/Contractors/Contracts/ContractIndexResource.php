<?php

namespace App\Http\Resources\Contractors\Contracts;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ContractIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор контракта */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->when(isset($this->created_at), $this->created_at),
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->when(isset($this->updated_at), $this->updated_at),
            /** @var string|null $archived_at Дата и время архивирования */
            'archived_at' => $this->when(isset($this->archived_at), $this->archived_at),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(isset($this->cabinet_id), $this->cabinet_id),
            /** @var string $number Номер контракта */
            'number' => $this->when(isset($this->number), (string) $this->number),
            /** @var string $date_from Дата контракта */
            'date_from' => $this->when(isset($this->date_from), $this->date_from),
            /** @var string|null $status_id Идентификатор статуса */
            'status_id' => $this->when(isset($this->status_id), $this->status_id),
            /** @var string $legal_entity_id Идентификатор юридического лица */
            'legal_entity_id' => $this->when(isset($this->legal_entity_id), $this->legal_entity_id),
            /** @var string $contractor_id Идентификатор контрагента */
            'contractor_id' => $this->when(isset($this->contractor_id), $this->contractor_id),
            /** @var string $currency_id Идентификатор валюты */
            'currency_id' => $this->when(isset($this->currency_id), $this->currency_id),
            /** @var string $type Тип контракта */
            'type' => $this->when(isset($this->type), (string) $this->type),
            /** @var string|null $code Код */
            'code' => $this->when(isset($this->code), $this->code),
            /** @var string|null $amount Сумма */
            'amount' => $this->when(isset($this->amount), $this->amount ? (string) $this->amount : null),
            /** @var string|null $comment Комментарий */
            'comment' => $this->when(isset($this->comment), $this->comment),
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->when(isset($this->employee_id), $this->employee_id),
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->when(isset($this->department_id), $this->department_id),
            /** @var bool $shared_access Общий доступ */
            'shared_access' => $this->when(isset($this->shared_access), (bool) $this->shared_access),
            /** @var bool $is_printed Напечатан */
            'is_printed' => $this->when(isset($this->is_printed), (bool) $this->is_printed),
            /** @var bool $is_sended Отправлен */
            'is_sended' => $this->when(isset($this->is_sended), (bool) $this->is_sended),
            /** @var array{id: string, name: string, color: string}|null $status Статус */
            'status' => $this->when(!empty($this->status), [
                'id' => $this->status['id'],
                'name' => $this->status['name'],
                'color' => $this->status['color'],
            ]),
            /** @var array{id: string, title: string}|null $contractor Контрагент */
            'contractor' => $this->when(!empty($this->contractor), [
                'id' => $this->contractor['id'],
                'title' => $this->contractor['title'],
            ]),
        ];
    }
}
