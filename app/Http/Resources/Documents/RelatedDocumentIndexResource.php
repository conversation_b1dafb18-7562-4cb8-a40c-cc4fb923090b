<?php

namespace App\Http\Resources\Documents;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class RelatedDocumentIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $documentable_id Идентификатор документа */
            'documentable_id' => $this->when(isset($this->documentable_id), $this->documentable_id),
            /** @var string $documentable_type Тип документа */
            'documentable_type' => $this->when(isset($this->documentable_type), (string) $this->documentable_type),
            /** @var string $tree_id Идентификатор дерева */
            'tree_id' => $this->when(isset($this->tree_id), $this->tree_id),
            /** @var int $lft Левая граница узла */
            'lft' => $this->when(isset($this->lft), (int) $this->lft),
            /** @var int $rgt Правая граница узла */
            'rgt' => $this->when(isset($this->rgt), (int) $this->rgt),
            /** @var string|null $parent_id Идентификатор родительского документа */
            'parent_id' => $this->when(isset($this->parent_id), $this->parent_id),
            /** @var array $children Дочерние документы */
            'children' => $this->when(isset($this->children), $this->children ? array_map(function ($child) {
                return RelatedDocumentIndexResource::make($child)->toArray($request);
            }, $this->children) : []),
        ];
    }
}
