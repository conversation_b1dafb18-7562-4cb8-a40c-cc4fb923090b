<?php

namespace App\Http\Resources\Goods\Categories;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductCategoryIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор категории */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->when(isset($this->created_at), $this->created_at),
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->when(isset($this->updated_at), $this->updated_at),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(isset($this->cabinet_id), $this->cabinet_id),
            /** @var string|null $parent_id Идентификатор родительской категории */
            'parent_id' => $this->when(isset($this->parent_id), $this->parent_id),
            /** @var string $name Название категории */
            'name' => $this->when(isset($this->name), (string) $this->name),
        ];
    }
}
