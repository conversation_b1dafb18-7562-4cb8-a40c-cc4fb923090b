<?php

namespace App\Http\Resources\Goods\Products;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор товара */
            'id' => $this->id,
            /** @var string|null $archived_at Дата и время архивирования */
            'archived_at' => $this->when(isset($this->archived_at), $this->archived_at),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(isset($this->cabinet_id), $this->cabinet_id),
            /** @var string $title Название товара */
            'title' => $this->when(isset($this->title), (string) $this->title),
            /** @var string $short_title Короткое название */
            'short_title' => $this->when(isset($this->short_title), (string) $this->short_title),
            /** @var int $type Тип товара */
            'type' => $this->when(isset($this->type), (int) $this->type),
            /** @var string|null $description Описание */
            'description' => $this->when(isset($this->description), $this->description),
            /** @var string|null $short_description Короткое описание */
            'short_description' => $this->when(isset($this->short_description), $this->short_description),
            /** @var bool $discounts_retail_sales Запретить скидки при продаже в розницу */
            'discounts_retail_sales' => $this->when(isset($this->discounts_retail_sales), (bool) $this->discounts_retail_sales),
            /** @var string|null $product_group_id Идентификатор группы товаров */
            'product_group_id' => $this->when(isset($this->product_group_id), $this->product_group_id),
            /** @var string|null $country_id Идентификатор страны */
            'country_id' => $this->when(isset($this->country_id), $this->country_id),
            /** @var string|null $article Артикул */
            'article' => $this->when(isset($this->article), $this->article),
            /** @var string|null $code Код товара */
            'code' => $this->when(isset($this->code), $this->code),
            /** @var int|null $inner_code Внутренний код */
            'inner_code' => $this->when(isset($this->inner_code), $this->inner_code ? (int) $this->inner_code : null),
            /** @var string|null $external_code Внешний код */
            'external_code' => $this->when(isset($this->external_code), $this->external_code),
            /** @var string|null $measurement_unit_id Идентификатор единицы измерения */
            'measurement_unit_id' => $this->when(isset($this->measurement_unit_id), $this->measurement_unit_id),
            /** @var string|null $brand_id Идентификатор бренда */
            'brand_id' => $this->when(isset($this->brand_id), $this->brand_id),
            /** @var object{value: string, currency_id: string} $min_price Минимальная цена */
            'min_price' => $this->when(isset($this->min_price), [
                'value' => (string) $this->min_price,
                'currency_id' => $this->when(isset($this->min_price_currency_id), $this->min_price_currency_id),
            ]),
            /** @var object{value: string, currency_id: string} $purchase_price Закупочная цена */
            'purchase_price' => $this->when(isset($this->purchase_price), [
                'value' => (string) $this->purchase_price,
                'currency_id' => $this->when(isset($this->purchase_price_currency_id), $this->purchase_price_currency_id),
            ]),
            /** @var array{length: string, width: string, height: string, weight: string, volume: string} $dimensions Габариты */
            'dimensions' => $this->when(isset($this->length), [
                'length' => (string) $this->length,
                'width' => (string) $this->width,
                'height' => (string) $this->height,
                'weight' => (string) $this->weight,
                'volume' => (string) $this->volume,
            ]),
            /** @var string|null $tax_id Идентификатор налога */
            'tax_id' => $this->when(isset($this->tax_id), $this->tax_id),
            /** @var string|null $tax_system Система налогообложения */
            'tax_system' => $this->when(isset($this->tax_system), $this->tax_system),
            /** @var string|null $indication_subject_calculation Признак предмета расчета */
            'indication_subject_calculation' => $this->when(isset($this->indication_subject_calculation), $this->indication_subject_calculation),
            /** @var int|null $threshold_type Тип порога */
            'threshold_type' => $this->when(isset($this->threshold_type), $this->threshold_type ? (int) $this->threshold_type : null),
            /** @var int|null $threshold_count Количество порога */
            'threshold_count' => $this->when(isset($this->threshold_count), $this->threshold_count ? (int) $this->threshold_count : null),
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->when(isset($this->deleted_at), $this->deleted_at),
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->when(isset($this->created_at), $this->created_at),
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->when(isset($this->updated_at), $this->updated_at),
            /** @var string|null $employee_id Идентификатор сотрудника */
            'employee_id' => $this->when(isset($this->employee_id), $this->employee_id),
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->when(isset($this->department_id), $this->department_id),
            /** @var string|null $contractor_id Идентификатор контрагента */
            'contractor_id' => $this->when(isset($this->contractor_id), $this->contractor_id),
            /** @var string|null $category_id Идентификатор категории */
            'category_id' => $this->when(isset($this->category_id), $this->category_id),
            /** @var bool $is_default Товар по умолчанию */
            'is_default' => $this->when(isset($this->is_default), (bool) $this->is_default),
        ];
    }
}
