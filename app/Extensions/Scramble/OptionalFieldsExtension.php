<?php

namespace App\Extensions\Scramble;

use Dedoc\Scramble\Extensions\TypeToSchemaExtension;
use Dedoc\Scramble\Support\Generator\Schema;
use Dedoc\Scramble\Support\Generator\Types\ObjectType;
use Dedoc\Scramble\Support\Type\Type;

class OptionalFieldsExtension extends TypeToSchemaExtension
{
    public function shouldHandle(Type $type): bool
    {
        return $type instanceof ObjectType;
    }

    public function toSchema(Type $type): Schema
    {
        if (!$type instanceof ObjectType) {
            return Schema::fromType($type);
        }

        // Получаем схему по умолчанию
        $schema = Schema::fromType($type);

        // Обрабатываем известные ресурсы с @optional полями
        $this->processKnownResources($schema);

        return $schema;
    }
    
    private function processKnownResources(Schema $schema): void
    {
        // Список ресурсов, которые содержат @optional поля
        $resourcesWithOptionalFields = [
            'AcceptanceItemIndexResource' => [
                'created_at', 'updated_at', 'vat_rate_id', 'country_id', 'gtd_number', 'product', 'currency'
            ],
        ];
        
        foreach ($resourcesWithOptionalFields as $resourceName => $optionalFields) {
            if ($this->schemaMatchesResource($schema, $resourceName)) {
                $this->removeRequiredFieldsFromSchema($schema, $optionalFields);
                break;
            }
        }
    }
    
    private function schemaMatchesResource(Schema $schema, string $resourceName): bool
    {
        if (!$schema->type instanceof ObjectType) {
            return false;
        }
        
        // Для AcceptanceItemIndexResource проверяем наличие характерных полей
        if ($resourceName === 'AcceptanceItemIndexResource') {
            $properties = $schema->type->properties ?? [];
            
            // Проверяем наличие data массива (для коллекций)
            if (isset($properties['data'])) {
                $dataProperty = $properties['data'];
                if ($dataProperty instanceof \Dedoc\Scramble\Support\Generator\Types\ArrayType && 
                    $dataProperty->items instanceof ObjectType) {
                    $itemProperties = $dataProperty->items->properties ?? [];
                    return isset($itemProperties['acceptance_id']) && isset($itemProperties['product_id']);
                }
            }
            
            // Или проверяем прямые свойства (для одиночных ресурсов)
            return isset($properties['acceptance_id']) && isset($properties['product_id']);
        }
        
        return false;
    }
    
    private function removeRequiredFieldsFromSchema(Schema $schema, array $optionalFields): void
    {
        if (!$schema->type instanceof ObjectType) {
            return;
        }
        
        // Если это коллекция с data
        if (isset($schema->type->properties['data'])) {
            $dataProperty = $schema->type->properties['data'];
            if ($dataProperty instanceof \Dedoc\Scramble\Support\Generator\Types\ArrayType && 
                $dataProperty->items instanceof ObjectType) {
                $this->removeRequiredFields($dataProperty->items, $optionalFields);
            }
        } else {
            // Если это обычный объект
            $this->removeRequiredFields($schema->type, $optionalFields);
        }
    }

    private function removeRequiredFields(ObjectType $objectType, array $optionalFields): void
    {
        // Проверяем различные способы доступа к required полям
        if (property_exists($objectType, 'required') && is_array($objectType->required)) {
            // Удаляем опциональные поля из списка обязательных
            $objectType->required = array_values(array_diff($objectType->required, $optionalFields));
        } elseif (method_exists($objectType, 'getRequired') && method_exists($objectType, 'setRequired')) {
            // Альтернативный способ для других версий Scramble
            $required = $objectType->getRequired();
            if (is_array($required)) {
                $objectType->setRequired(array_values(array_diff($required, $optionalFields)));
            }
        }
    }
}
