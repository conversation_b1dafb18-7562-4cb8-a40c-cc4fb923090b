<?php

$files = [
    'app/Http/Resources/Procurement/Acceptances/AcceptanceIndexResource.php',
    'app/Http/Resources/Procurement/Acceptances/AcceptanceItemIndexResource.php',
    'app/Http/Resources/Procurement/VendorOrders/VendorOrderIndexResource.php',
    'app/Http/Resources/Procurement/VendorOrders/VendorOrderItemIndexResource.php',
    'app/Http/Resources/References/Currencies/CurrencyIndexResource.php',
    'app/Http/Resources/References/GlobalCurrencies/GlobalCurrencyIndexResource.php',
    'app/Http/Resources/References/LegalEntities/LegalEntityIndexResource.php',
    'app/Http/Resources/References/MeasurementUnits/MeasurementUnitGroupIndexResource.php',
    'app/Http/Resources/References/MeasurementUnits/MeasurementUnitIndexResource.php',
    'app/Http/Resources/References/Packings/PackingIndexResource.php',
    'app/Http/Resources/References/ProfitTaxRates/ProfitTaxRateIndexResource.php',
    'app/Http/Resources/References/SalesChannels/SalesChannelIndexResource.php',
    'app/Http/Resources/References/SalesChannelTypes/SalesChannelTypeIndexResource.php',
    'app/Http/Resources/References/VatRates/VatRateIndexResource.php',
    'app/Http/Resources/Sales/ComissionReports/IssuedComissionReportIndexResource.php',
    'app/Http/Resources/Sales/ComissionReports/ReceivedComissionReportIndexResource.php',
    'app/Http/Resources/Sales/CustomerOrders/CustomerOrderIndexResource.php',
    'app/Http/Resources/Sales/CustomerOrders/CustomerOrderItemIndexResource.php',
    'app/Http/Resources/Sales/Shipments/ShipmentIndexResource.php',
    'app/Http/Resources/Sales/Shipments/ShipmentItemIndexResource.php',
    'app/Http/Resources/User/Bookmarks/BookmarkIndexResource.php',
    'app/Http/Resources/User/UserViewSettings/UserViewSettingsIndexResource.php',
    'app/Http/Resources/Warehouses/Contacts/WarehouseAddressIndexResource.php',
    'app/Http/Resources/Warehouses/Contacts/WarehousePhoneIndexResource.php',
    'app/Http/Resources/Workspace/Cabinet/CabinetInviteIndexResource.php',
    'app/Http/Resources/Workspace/Departments/DepartmentIndexResource.php',
    'app/Http/Resources/Workspace/Employees/EmployeeIndexResource.php'
];

foreach ($files as $file) {
    if (!file_exists($file)) {
        echo "File not found: $file\n";
        continue;
    }
    
    $content = file_get_contents($file);
    
    // Fix broken when() calls
    $content = preg_replace('/\$this->when\(isset\(\$this->\), ([^)]+)\)/', '$this->when(isset($this->$1), $this->$1)', $content);
    
    file_put_contents($file, $content);
    echo "Fixed: $file\n";
}

echo "Fix complete!\n";
