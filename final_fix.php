<?php

// Get all IndexResource files
$files = glob('app/Http/Resources/**/*IndexResource.php', GLOB_BRACE);
$files = array_merge($files, glob('app/Http/Resources/*IndexResource.php'));
$files = array_merge($files, glob('app/Http/Resources/*/*IndexResource.php'));
$files = array_merge($files, glob('app/Http/Resources/*/*/*IndexResource.php'));
$files = array_merge($files, glob('app/Http/Resources/*/*/*/*IndexResource.php'));

$files = array_unique($files);

foreach ($files as $file) {
    if (!file_exists($file) || strpos($file, 'Collection') !== false) {
        continue;
    }
    
    $content = file_get_contents($file);
    $originalContent = $content;
    
    // Pattern to match field assignments that are not 'id' and don't already have when()
    $pattern = "/('(?!id')[^']+') => (\\\$this->(?!when)[^,\n;]+)/";
    
    $content = preg_replace_callback($pattern, function($matches) {
        $field = $matches[1];
        $value = trim($matches[2]);
        
        // Skip if already has when() or is complex expression
        if (strpos($value, 'when(') !== false || strpos($value, '[') !== false) {
            return $matches[0];
        }
        
        // Extract property name from $this->property
        if (preg_match('/\$this->([a-zA-Z_][a-zA-Z0-9_]*)/', $value, $propMatches)) {
            $property = $propMatches[1];
            return "$field => \$this->when(isset(\$this->$property), $value)";
        }
        
        return $matches[0];
    }, $content);
    
    if ($content !== $originalContent) {
        file_put_contents($file, $content);
        echo "Updated: $file\n";
    }
}

echo "Final processing complete!\n";
